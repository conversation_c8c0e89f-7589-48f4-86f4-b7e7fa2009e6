<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Effet Néon</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand-dark': '#0A0F1F',
                        'brand-surface': '#1A2035',
                        'brand-surface-light': 'rgba(26, 32, 53, 0.5)',
                        'brand-blue': '#4285F4',
                        'brand-purple': '#9B72F5',
                        'brand-light': '#E0E8FF',
                        'brand-muted': '#8D98B2',
                    }
                }
            }
        }
    </script>
    <style>
        /* Effet néon laser pour les cartes - Copie exacte du code qui fonctionnait */

        @property --angle {
          syntax: "<angle>";
          initial-value: 0deg;
          inherits: false;
        }

        :root {
          --gemini-blue-light: #2190F6;
          --gemini-blue-purple: #6689EF;
          --gemini-purple: rgb(77, 70, 175);
          --gemini-salmon: rgb(235, 73, 114);
        }

        /* Classe utilitaire pour l'effet néon laser - EXACTEMENT comme glowing-card */
        .glowing-effect {
          position: relative;
        }

        .glowing-effect::before,
        .glowing-effect::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          z-index: -1;
          width: 100%;
          height: 100%;
          border-radius: inherit;
        }

        .glowing-effect::before,
        .glowing-effect::after {
          background-image: conic-gradient(
            from var(--angle),
            transparent 0%,
            transparent 60%,
            var(--gemini-salmon),
            var(--gemini-purple),
            var(--gemini-blue-purple),
            var(--gemini-blue-light)
          );
        }

        .glowing-effect::before {
          filter: blur(0.2rem) brightness(2.5);
          transform: translate(-50%, -50%) scale(1.01);
        }

        .glowing-effect::after {
          filter: brightness(2.5);
        }

        .glowing-effect::before,
        .glowing-effect::after {
          opacity: 0;
          transition: opacity 0.4s ease-in-out;
        }

        .glowing-effect:hover::after {
          opacity: 1;
          animation: spin 5s linear infinite;
        }

        .glowing-effect:hover::before {
          opacity: 0.8;
          animation: spin 5s linear infinite;
        }

        @keyframes spin {
          from { --angle: 0deg; }
          to { --angle: 360deg; }
        }

        /* L'effet reste en arrière-plan grâce au z-index: -1 des pseudo-éléments */
    </style>
</head>
<body class="bg-brand-dark text-brand-light min-h-screen p-8">
    <div class="container mx-auto max-w-4xl">
        <h1 class="text-4xl font-bold mb-8 text-center">Test Effet Néon</h1>
        
        <!-- Test 1: Div simple avec effet néon -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold mb-4">Test 1: Div simple avec bg-brand-surface</h2>
            <div class="glowing-effect bg-brand-surface rounded-lg p-8 border border-brand-surface">
                <h3 class="text-xl font-bold mb-2">Titre de test</h3>
                <p class="text-brand-muted">Ceci est un test de l'effet néon. L'effet devrait apparaître derrière cette div au survol.</p>
            </div>
        </div>

        <!-- Test 2: Div avec opacité 50% -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold mb-4">Test 2: Div avec bg-brand-surface/50 (problématique)</h2>
            <div class="glowing-effect bg-brand-surface/50 rounded-lg p-8 border border-brand-surface">
                <h3 class="text-xl font-bold mb-2">Titre de test</h3>
                <p class="text-brand-muted">Avec opacité 50%, l'effet néon peut se voir à travers le fond.</p>
            </div>
        </div>

        <!-- Test 3: Div sans effet néon pour comparaison -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold mb-4">Test 3: Div normale sans effet (comparaison)</h2>
            <div class="bg-brand-surface rounded-lg p-8 border border-brand-surface">
                <h3 class="text-xl font-bold mb-2">Titre de test</h3>
                <p class="text-brand-muted">Div normale sans effet néon pour comparaison.</p>
            </div>
        </div>

        <!-- Test 4: Div avec classes Tailwind potentiellement conflictuelles -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold mb-4">Test 4: Avec classes potentiellement conflictuelles</h2>
            <div class="glowing-effect bg-brand-surface backdrop-blur-sm rounded-lg p-8 border border-brand-surface relative z-10">
                <h3 class="text-xl font-bold mb-2">Titre de test</h3>
                <p class="text-brand-muted">Test avec backdrop-blur-sm et z-10 qui pourraient créer des conflits.</p>
            </div>
        </div>
    </div>
</body>
</html>
